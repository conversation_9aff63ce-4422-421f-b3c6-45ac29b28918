using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;

namespace Axon.Core.Authentication.Services;

public interface IAuthenticationService
{
    Task<ClaimsPrincipal?> ValidateTokenAsync(string token, TokenValidationParameters validationParameters);
    Task SignInUserAsync(HttpContext httpContext, ClaimsPrincipal claimsPrincipal);
}
