using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Controllers;
using Axon.Core.Authentication.Interfaces;
using Axon.Core.Authentication.Services;
using Axon.Core.Authentication.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using NSubstitute;
using Shouldly;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Xunit;
using static Axon.Core.Authentication.Tests.TestConstants;
using static Axon.Core.Authentication.Tests.TestHelpers;

namespace Axon.Core.Authentication.Tests.Controllers;

public class AxonLoginControllerTests
{
    private readonly ITokenService tokenService;
    private readonly IAuthenticationService authenticationService;
    private readonly IJwtKeyProvider jwtKeyProvider;
    private readonly IOptionsMonitor<JwtAuthenticationOptions> options;
    private readonly ILogger<AxonLoginController> logger;
    private readonly AxonLoginController controller;
    private readonly JwtAuthenticationOptions jwtOptions;

    public AxonLoginControllerTests()
    {
        tokenService = Substitute.For<ITokenService>();
        authenticationService = Substitute.For<IAuthenticationService>();
        jwtKeyProvider = Substitute.For<IJwtKeyProvider>();
        options = Substitute.For<IOptionsMonitor<JwtAuthenticationOptions>>();
        logger = Substitute.For<ILogger<AxonLoginController>>();

        jwtOptions = new JwtAuthenticationOptions
        {
            DefaultRedirectUrl = DefaultRedirectUrl,
            Issuer = Issuer,
            Audience = Audience,
            PublicKeyApiHost = PublicKeyApiHost
        };

        options.CurrentValue.Returns(jwtOptions);

        var mockKeys = new List<JsonWebKey> { new JsonWebKey() };
        jwtKeyProvider.GetKeyAsync(Arg.Any<string>()).Returns(mockKeys);

        controller = new AxonLoginController(tokenService, authenticationService, jwtKeyProvider, options, logger)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            }
        };
    }

    [Fact]
    public async Task Login_WithMissingCode_ShouldReturnBadRequest()
    {
        // Act
        var result = await controller.Login(string.Empty, EmptyRedirectUrl);

        // Assert
        result.ShouldBeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.CodeRequired);
    }

    [Fact]
    public async Task Login_WithMissingRedirectUrl_ShouldUseDefaultRedirectUrl()
    {
        // Arrange
        var testCode = GenerateTestCode();
        var testToken = GenerateTestToken();
        var claimsPrincipal = CreateAuthenticatedClaimsPrincipal();

        SetupSuccessfulTokenFlow(tokenService, authenticationService, testCode, testToken, claimsPrincipal);

        // Act
        var result = await controller.Login(testCode, string.Empty);

        // Assert
        result.ShouldBeOfType<RedirectResult>();
        var redirectResult = (RedirectResult)result;
        redirectResult.Url.ShouldBe(jwtOptions.DefaultRedirectUrl);

        await VerifySuccessfulTokenFlow(tokenService, authenticationService, testCode, testToken, claimsPrincipal);
    }

    [Fact]
    public async Task Login_WithValidParameters_ShouldReturnRedirect()
    {
        // Arrange
        var testCode = GenerateTestCode();
        var testToken = GenerateTestToken();
        var claimsPrincipal = CreateAuthenticatedClaimsPrincipal();

        SetupSuccessfulTokenFlow(tokenService, authenticationService, testCode, testToken, claimsPrincipal);

        // Act
        var result = await controller.Login(testCode, RedirectUrl);

        // Assert
        result.ShouldBeOfType<RedirectResult>();
        var redirectResult = (RedirectResult)result;
        redirectResult.Url.ShouldBe(RedirectUrl);

        await VerifySuccessfulTokenFlow(tokenService, authenticationService, testCode, testToken, claimsPrincipal);
    }

    [Fact]
    public async Task Login_WithTokenValidationFailure_ShouldReturnUnauthorized()
    {
        // Arrange
        var testCode = GenerateTestCode();
        var testToken = GenerateTestToken();

        tokenService.GetTokenAsync(testCode).Returns(testToken);
        authenticationService.ValidateTokenAsync(testToken, Arg.Any<TokenValidationParameters>()).Returns((ClaimsPrincipal)null!);

        // Act
        var result = await controller.Login(testCode, RedirectUrl);

        // Assert
        result.ShouldBeOfType<UnauthorizedObjectResult>();
        var unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.TokenValidationFailed);
    }

    [Fact]
    public async Task Login_WithEmptyTokenResponse_ShouldReturnUnauthorized()
    {
        // Arrange
        var testCode = GenerateTestCode();

        tokenService.GetTokenAsync(testCode).Returns(string.Empty);

        // Act
        var result = await controller.Login(testCode, RedirectUrl);

        // Assert
        result.ShouldBeOfType<UnauthorizedObjectResult>();
        var unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.TokenRetrievalFailed);
    }

    [Fact]
    public async Task Login_WithTokenServiceException_ShouldReturnInternalServerError()
    {
        // Arrange
        var testCode = GenerateTestCode();

        tokenService.GetTokenAsync(testCode)
            .Returns(Task.FromException<string>(new InvalidOperationException(ExceptionMessage)));

        // Act
        var result = await controller.Login(testCode, RedirectUrl);

        // Assert
        result.ShouldBeOfType<ObjectResult>();
        var objectResult = (ObjectResult)result;
        objectResult.StatusCode.ShouldBe(AuthenticationConstants.HttpStatusCodes.InternalServerError);
        objectResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.AuthenticationError);
    }

    [Fact]
    public async Task Login_WithUnauthenticatedClaimsPrincipal_ShouldReturnUnauthorized()
    {
        // Arrange
        var testCode = GenerateTestCode();
        var testToken = GenerateTestToken();
        var unauthenticatedPrincipal = CreateUnauthenticatedClaimsPrincipal();

        tokenService.GetTokenAsync(testCode).Returns(testToken);
        authenticationService.ValidateTokenAsync(testToken, Arg.Any<TokenValidationParameters>()).Returns(unauthenticatedPrincipal);

        // Act
        var result = await controller.Login(testCode, RedirectUrl);

        // Assert
        result.ShouldBeOfType<UnauthorizedObjectResult>();
        var unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.TokenValidationFailed);
    }
}
