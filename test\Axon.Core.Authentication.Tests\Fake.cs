﻿using System;
using System.Linq;

namespace Axon.Core.Authentication.Tests;

public static class Fake
{
    private static readonly Random Random = new();
    public static string GetRandomString(int length)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[Random.Next(s.Length)]).ToArray());
    }

}
