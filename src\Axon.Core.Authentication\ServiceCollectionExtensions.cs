﻿using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Events;
using Axon.Core.Authentication.Interfaces;
using Axon.Core.Authentication.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.Net.Http;

namespace Axon.Core.Authentication;

public static class ServiceCollectionExtensions
{

    public static IServiceCollection AddAxonAuthentication(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<JwtAuthenticationOptions>? configureOptions)
    {
        var optionsSection = configuration.GetSection(JwtAuthenticationOptions.AxonCoreAuthenticationConfigKey);
        services.Configure<JwtAuthenticationOptions>(optionsSection);

        if (configureOptions != null)
        {
            services.Configure(configureOptions);
        }

        services.AddHttpClient<IJwtKeyProvider, JwtKeyProvider>().ConfigurePrimaryHttpMessageHandler(() =>
            new SocketsHttpHandler() { PooledConnectionLifetime = TimeSpan.FromMinutes(15) });

        services.AddHttpClient<ITokenService, TokenService>().ConfigurePrimaryHttpMessageHandler(() =>
            new SocketsHttpHandler() { PooledConnectionLifetime = TimeSpan.FromMinutes(15) });

        services.AddScoped<IAuthenticationService, AuthenticationService>();

        services.AddMemoryCache();
        services.AddSingleton<AxonJwtBearerEvents>();
        services.AddSingleton<IJwtKeyProvider, JwtKeyProvider>();

        services.AddAuthentication(JwtBearerAuthenticationDefaults.AuthenticationScheme)
            .AddJwtBearer(JwtBearerAuthenticationDefaults.AuthenticationScheme, options =>
            {
                var serviceProvider = services.BuildServiceProvider();
                var events = serviceProvider.GetRequiredService<AxonJwtBearerEvents>();

                options.Events = events;
                options.SaveToken = true;
                options.RequireHttpsMetadata = true;
                options.MapInboundClaims = false;
            });

        return services;
    }
}